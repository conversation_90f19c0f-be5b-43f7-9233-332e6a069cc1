1. 增加接口：用来返回hook调用耗时。
    参数： sn， 
    返回值： hook耗时
    功能： zlm收到调用后立即调用心跳hook，hook中携带sn信息， zlm收到hook返回后计算此次hook的耗时，然后返回耗时
    用途： 1. 可以用来检测hook配置是否正常
          2. 检测wvp与zlm连接的健康程度
2. 增加接口：查询loadMP4File的录像播放位置
   参数： app， steam，
   返回： 当前播放的hook位置
   功能： zlm收到请求后查询流的seek位置并返回
   用途： 1. 获取当前播放位置，可以在页面同步显示时间
         2. 用来时快进五秒 快退五秒类似的操作
3. loadMP4File扩展， 增加默认seek值和默认倍速，开始时直接从这个位置开始，并使用指定的倍速
4. 支持下载指定取件的mp4文件，这个区间可能包括多个文件和文件的一部分。
5. 希望seek接口和倍速接口可以去除schema的必选，作为可选项，不传则默认全部
