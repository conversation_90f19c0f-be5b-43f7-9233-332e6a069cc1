


# 此配置文件只是用作展示所有配置项， 不可直接使用


spring:
    cache:
        type: redis
    thymeleaf:
        cache: false
    # 设置接口超时时间
    mvc:
        async:
            request-timeout: 20000
    # [可选]上传文件大小限制
    servlet:
        multipart:
            max-file-size: 10MB
            max-request-size: 100MB
    # REDIS数据库配置
    redis:
        # [必须修改] Redis服务器IP, REDIS安装在本机的,使用127.0.0.1
        host: 127.0.0.1
        # [必须修改] 端口号
        port: 6379
        # [可选] 数据库 DB
        database: 6
        # [可选] 访问密码,若你的redis服务器没有设置密码，就不需要用密码去连接
        password:
        # [可选] 超时时间
        timeout: 10000
        # [可选] 一个pool最多可分配多少个jedis实例
        poolMaxTotal: 1000
        # [可选] 一个pool最多有多少个状态为idle(空闲)的jedis实例
        poolMaxIdle: 500
        # [可选] 最大的等待时间(秒)
        poolMaxWait: 5
    # [必选] jdbc数据库配置
    datasource:
        # kingbase配置
        #        type: com.zaxxer.hikari.HikariDataSource
        #        driver-class-name: com.kingbase8.Driver
        #        url: jdbc:kingbase8://************:54321/wvp?useUnicode=true&characterEncoding=utf8
        #        username: system
        #        password: system
        # postgresql配置
        #    type: com.zaxxer.hikari.HikariDataSource
        #    driver-class-name: org.postgresql.Driver
        #    url: *******************************************
        #    username: root
        #    password: SYceshizu1234
        # mysql配置
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************************************
        username: root
        password: root123
        hikari:
            connection-timeout: 20000             # 是客户端等待连接池连接的最大毫秒数
            initialSize: 50                       # 连接池初始化连接数
            maximum-pool-size: 200                # 连接池最大连接数
            minimum-idle: 10                       # 连接池最小空闲连接数
            idle-timeout: 300000                  # 允许连接在连接池中空闲的最长时间（以毫秒为单位）
            max-lifetime: 1200000                 # 是池中连接关闭后的最长生命周期（以毫秒为单位)



# 修改分页插件为 postgresql， 数据库类型为mysql不需要
#pagehelper:
#    helper-dialect: postgresql

# [可选] WVP监听的HTTP端口, 网页和接口调用都是这个端口
server:
    port: 18080
    # [可选] HTTPS配置， 默认不开启
    ssl:
        # [可选] 是否开启HTTPS访问
        enabled: false
        # [可选] 证书文件路径，放置在resource/目录下即可，修改xxx为文件名
        key-store: classpath:xxx.jks
        # [可选] 证书密码
        key-store-password: password
        # [可选] 证书类型， 默认为jks，根据实际修改
        key-store-type: JKS
        # 配置证书可以使用如下两项，如上面二选一即可
        # PEM 编码证书
        certificate: xx.pem
        #   私钥文件
        certificate-private-key: xx.key

# 作为28181服务器的配置
sip:
    # [必须修改] 本机的IP，对应你的网卡，监听什么ip就是使用什么网卡，
    # 如果要监听多张网卡，可以使用逗号分隔多个IP， 例如： ***********,********
    # 如果不明白，就使用0.0.0.0，大部分情况都是可以的
    # 请不要使用127.0.0.1，任何包括localhost在内的域名都是不可以的。
    ip: 0.0.0.0
    # [可选] 没有任何业务需求，仅仅是在前端展示的时候用
    show-ip: *************
    # [可选] 28181服务监听的端口
    port: 5060
    # 根据国标6.1.2中规定，domain宜采用ID统一编码的前十位编码。国标附录D中定义前8位为中心编码（由省级、市级、区级、基层编号组成，参照GB/T 2260-2007）
    # 后两位为行业编码，定义参照附录D.3
    # 3701020049标识山东济南历下区 信息行业接入
    # [可选]
    domain: 4401020049
    # [可选]
    id: 44010200492000000001
    # [可选] 默认设备认证密码，后续扩展使用设备单独密码, 移除密码将不进行校验
    password: admin123
    # [可选] 国标级联注册失败，再次发起注册的时间间隔。 默认60秒
    register-time-interval: 60
    # [可选] 云台控制速度
    ptz-speed: 50
    # TODO [可选] 收到心跳后自动上线， 重启服务后会将所有设备置为离线，默认false，等待注册后上线。设置为true则收到心跳设置为上线。
    # keepalliveToOnline: false
    # 是否存储alarm信息
    alarm: false
    # 命令发送等待回复的超时时间, 单位:毫秒
    timeout: 1000

# 做为JT1078服务器的配置
ftp:
  #[必须修改] 是否开启1078的服务
  enable: true
  port: 2121
  passive-ports: 10000-10500

# 做为JT1078服务器的配置
jt1078:
    #[必须修改] 是否开启1078的服务
    enable: true
    #[必修修改] 1708设备接入的端口
    port: 21078
    #[可选] 设备鉴权的密码
    password: admin123


#zlm 默认服务器配置
media:
    # [必须修改] zlm服务器唯一id，用于触发hook时区别是哪台服务器,general.mediaServerId
    id:
    # [必须修改] zlm服务器的内网IP
    ip: *************
    # [可选] 有公网IP就配置公网IP, 不可用域名
    wan_ip:
    # [可选] 返回流地址时的ip，置空使用 media.ip
    stream-ip:
    # [可选] wvp在国标信令中使用的ip，此ip为摄像机可以访问到的ip， 置空使用 media.ip
    sdp-ip:
    # [可选] zlm服务器访问WVP所使用的IP, 默认使用127.0.0.1，zlm和wvp没有部署在同一台服务器时必须配置
    hook-ip: *************
    # [必须修改] zlm服务器的http.port
    http-port: 80
    # [可选] zlm服务器的http.sslport, 置空使用zlm配置文件配置
    http-ssl-port:
    # [可选] zlm服务器的rtmp.port, 置空使用zlm配置文件配置
    rtmp-port:
    # [可选] zlm服务器的rtmp.sslport, 置空使用zlm配置文件配置
    rtmp-ssl-port:
    # [可选] zlm服务器的 rtp_proxy.port, 置空使用zlm配置文件配置
    rtp-proxy-port:
    # [可选] zlm服务器的 rtsp.port, 置空使用zlm配置文件配置
    rtsp-port:
    # [可选] zlm服务器的 rtsp.sslport, 置空使用zlm配置文件配置
    rtsp-ssl-port:
    # [可选] 是否自动配置ZLM, 如果希望手动配置ZLM, 可以设为false, 不建议新接触的用户修改
    auto-config: true
    # [可选] zlm服务器的hook.admin_params=secret
    secret: 035c73f7-bb6b-4889-a715-d9eb2d1925cc
    # 录像路径
    record-path: ./www/record
    # 录像保存时长
    record-day: 7
    # 启用多端口模式, 多端口模式使用端口区分每路流，兼容性更好。 单端口使用流的ssrc区分， 点播超时建议使用多端口测试
    rtp:
        # [可选] 是否启用多端口模式, 开启后会在portRange范围内选择端口用于媒体流传输
        enable: true
        # [可选] 在此范围内选择端口用于媒体流传输, 必须提前在zlm上配置该属性，不然自动配置此属性可能不成功
        port-range: 30000,30500 # 端口范围
        # [可选] 国标级联在此范围内选择端口发送媒体流，请不要与收流端口范围重合
        send-port-range: 50502,50506 # 端口范围
    # 录像辅助服务， 部署此服务可以实现zlm录像的管理与下载， 0 表示不使用
    record-assist-port: 0

# [可选] 日志配置, 如果不需要在jar外修改日志内容那么可以不配置此项
logging:
    config: classpath:logback-spring.xml

# [根据业务需求配置]
user-settings:
    # 服务ID，不写则为000000
    server-id:
    # [可选] 自动点播， 使用固定流地址进行播放时，如果未点播则自动进行点播, 需要rtp.enable=true
    auto-apply-play: false
    # [可选] 部分设备需要扩展SDP，需要打开此设置
    senior-sdp: false
    # 保存移动位置历史轨迹：true:保留历史数据，false:仅保留最后的位置(默认)
    save-position-history: false
    # 点播/录像回放 等待超时时间,单位：毫秒
    play-timeout: 18000
    # 获取设备录像数据超时时间,单位：毫秒
    record-info-timeout: 10000
    # 上级点播等待超时时间,单位：毫秒
    platform-play-timeout: 60000
    # 是否开启接口鉴权
    interface-authentication: true
    # 接口鉴权例外的接口, 即不进行接口鉴权的接口,尽量详细书写，尽量不用/**，至少两级目录
    interface-authentication-excludes:
        - /api/v1/**
    # 推流直播是否录制
    record-push-live: true
    # 国标是否录制
    record-sip: true
    # 使用推流状态作为推流通道状态
    use-pushing-as-status: true
    # 使用来源请求ip作为streamIp,当且仅当你只有zlm节点它与wvp在一起的情况下开启
    use-source-ip-as-stream-ip: false
    # 国标点播 按需拉流, true：有人观看拉流，无人观看释放， false：拉起后不自动释放
    stream-on-demand: true
    # 推流鉴权， 默认开启
    push-authority: true
    # 设备上线时是否自动同步通道
    sync-channel-on-device-online: false
    # 国标级联语音喊话发流模式 * UDP:udp传输 TCP-ACTIVE：tcp主动模式 TCP-PASSIVE：tcp被动模式
    broadcast-for-platform: UDP
    # 是否使用设备来源Ip作为回复IP， 不设置则为 false
    sip-use-source-ip-as-remote-address: false
    # 是否开启sip日志
    sip-log: true
    # 是否开启sql日志
    sql-log: true
    # 消息通道功能-缺少国标ID是否给所有上级发送消息
    send-to-platforms-when-id-lost: true
    # 保持通道状态，不接受notify通道状态变化， 兼容海康平台发送错误消息
    refuse-channel-status-channel-form-notify: false
    # 设置notify缓存队列最大长度，超过此长度的数据将返回486 BUSY_HERE，消息丢弃, 默认10000
    max-notify-count-queue: 10000
    # 设备/通道状态变化时发送消息
    device-status-notify: false
    # 上级平台点播时不使用上级平台指定的ssrc，使用自定义的ssrc，参考国标文档-点播外域设备媒体流SSRC处理方式
    use-custom-ssrc-for-parent-invite: true
    # 国标级联离线后多久重试一次注册
    register-again-after-time: 60
    # 国标续订方式，true为续订，每次注册在同一个会话里，false为重新注册，每次使用新的会话
    register-keep-int-dialog: false
    # 开启接口文档页面。 默认开启，生产环境建议关闭，遇到swagger相关的漏洞时也可以关闭
    doc-enable: true
    # 跨域配置，不配置此项则允许所有跨域请求，配置后则只允许配置的页面的地址请求， 可以配置多个
    allowed-origins:
        - http://localhost:8008
        - http://***********:8008
    # 国标设备离线后的上线策略，
    # 0： 国标标准实现，设备离线后不回复心跳，直到设备重新注册上线，
    # 1（默认）： 对于离线设备，收到心跳就把设备设置为上线，并更新注册时间为上次这次心跳的时间。防止过期时间判断异常
    gb-device-online: 0
    # 登录超时时间(分钟)，
    login-timeout: 30
    # jwk文件路径，若不指定则使用resources目录下的jwk.json
    jwk-file: classpath:jwk.json
    # wvp集群模式下如果注册向上级的wvp奔溃，则自动选择一个其他wvp继续注册到上级
    auto-register-platform: true
    # 按需发送位置， 默认发送移动位置订阅时如果位置不变则不发送， 设置为false按照国标间隔持续发送
    send-position-on-demand: true
    # 部分设备会在短时间内发送大量注册， 导致协议栈内存溢出， 开启此项可以防止这部分设备注册， 避免服务崩溃，但是会降低系统性能， 描述如下
    # 默认值为 true。
    # 将此设置为 false 会使 Stack 在 Server Transaction 进入 TERMINATED 状态后关闭服务器套接字。
    # 这允许服务器防止客户端发起的基于 TCP 的拒绝服务攻击（即发起数百个客户端事务）。
    # 如果为 true（默认作），则堆栈将保持套接字打开，以便以牺牲线程和内存资源为代价来最大化性能 - 使自身容易受到 DOS 攻击。
    sip-cache-server-connections: true

# 关闭在线文档（生产环境建议关闭）
springdoc:
    api-docs:
        enabled: false
    swagger-ui:
        enabled: false
