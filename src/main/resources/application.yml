spring:
  application:
    name: wvp
  # 设置接口超时时间
  mvc:
    async:
      request-timeout: 20000
  thymeleaf:
    cache: false
  # [可选]上传文件大小限制
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  cache:
    type: redis
  # REDIS数据库配置
  redis:
    # [必须修改] Redis服务器IP, REDIS安装在本机的,使用127.0.0.1
    host: ***************
    # [必须修改] 端口号
    port: 16379
    # [可选] 数据库 DB
    database: 6
    # [可选] 访问密码,若你的redis服务器没有设置密码，就不需要用密码去连接
    password: ybDa_2023Data.
    # [可选] 超时时间
    timeout: 10000
    # mysql数据源
  datasource:
    #    type: com.zaxxer.hikari.HikariDataSource
    #    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: jdbc:mysql://**************:13306/wvp?useUnicode=true&characterEncoding=UTF8&rewriteBatchedStatements=true&serverTimezone=PRC&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true
    #    username: root
    #    password: YBda1234.
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************
    username: root
    password: YBda1234.

#[可选] WVP监听的HTTP端口, 网页和接口调用都是这个端口
server:
  port: 18080
  # [可选] HTTPS配置， 默认不开启
  ssl:
    # [可选] 是否开启HTTPS访问
    enabled: false
    # [可选] 证书文件路径，放置在resource/目录下即可，修改xxx为文件名
    key-store: classpath:test.monitor.89iot.cn.jks
    # [可选] 证书密码
    key-store-password: gpf64qmw
    # [可选] 证书类型， 默认为jks，根据实际修改
    key-store-type: JKS

# 作为28181服务器的配置
sip:
  # [可选] 28181服务监听的端口
  port: 15060
  # 根据国标6.1.2中规定，domain宜采用ID统一编码的前十位编码。国标附录D中定义前8位为中心编码（由省级、市级、区级、基层编号组成，参照GB/T 2260-2007）
  # 后两位为行业编码，定义参照附录D.3
  # 3701020049标识山东济南历下区 信息行业接入
  # [可选]
  domain: 5115250000
  # [可选]
  id: 51152500002000000001
  # [可选] 默认设备认证密码，后续扩展使用设备单独密码, 移除密码将不进行校验
  password: ybda1234
  # 是否存储alarm信息
  alarm: false

#zlm 默认服务器配置
media:
  id: zlmediakit-local
  # [必须修改] zlm服务器的内网IP
  ip: 127.0.0.1
  # [必须修改] zlm服务器的http.port
  http-port: 9092
  # [必选选] zlm服务器的hook.admin_params=secret
  secret: TWSYFgYJOQWB4ftgeYut8DW4wbs7pQnj
  # 启用多端口模式, 多端口模式使用端口区分每路流，兼容性更好。 单端口使用流的ssrc区分， 点播超时建议使用多端口测试
  rtp:
    # [可选] 是否启用多端口模式, 开启后会在portRange范围内选择端口用于媒体流传输
    enable: true
    # [可选] 在此范围内选择端口用于媒体流传输, 必须提前在zlm上配置该属性，不然自动配置此属性可能不成功
    port-range: 30000,35000 # 端口范围
    # [可选] 国标级联在此范围内选择端口发送媒体流,
    send-port-range: 50000,55000 # 端口范围
# [根据业务需求配置]
user-settings:
  # 点播/录像回放 等待超时时间,单位：毫秒
  play-timeout: 180000
  # [可选] 自动点播， 使用固定流地址进行播放时，如果未点播则自动进行点播, 需要rtp.enable=true
  auto-apply-play: true
  # 推流直播是否录制
  record-push-live: true
  # 国标是否录制
  record-sip: true
  # 国标点播 按需拉流, true：有人观看拉流，无人观看释放， false：拉起后不自动释放
  stream-on-demand: true
  # 是否返回Date属性，true：不返回，避免摄像头通过该参数自动校时，false：返回，摄像头可能会根据该时间校时
  disable-date-header: false


