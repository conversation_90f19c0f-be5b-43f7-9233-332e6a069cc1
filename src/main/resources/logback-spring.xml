<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
	<!--定义日志文件的存储地址 -->
	<springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
	<property name="LOG_HOME" value="logs" />

	<substitutionProperty name="log.pattern"
						  value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]) %clr(%5p) %clr(---){faint} %clr(%logger{50}){cyan}%clr(:) %clr(%L){cyan} %m%n%wEx"/>
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
	<conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
	<conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

	<!-- 控制台输出 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>${log.pattern}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter" >
			<!--与ThresholdFilter的区别,允许onmatch-->
			<!--设置日志级别 接收info级别的日志-->
			<level>DEBUG</level>
		</filter>
	</appender>

	<!-- WebSocket -->
	<appender name="websocket" class="com.genersoft.iot.vmp.conf.webLog.WebSocketAppender">
		<encoder>
			<pattern>${log.pattern}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!-- 按照每天生成日志文件 DEBUG以上级别的日志,仅用于测试环境,正式环境为info级别以上的日志-->
	<appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">

		<!-- 文件路径 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--历史日志文件输出的文件名 -->
			<FileNamePattern>${LOG_HOME}/wvp-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--日志文件保留天数 -->
			<MaxHistory>30</MaxHistory>
			<maxFileSize>20MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<!--与ThresholdFilter的区别,允许onmatch-->
			<!--设置日志级别 接收info级别的日志-->
			<level>DEBUG</level>
		</filter>
	</appender>

	<!-- 生成 SIP日志追加 -->
	<appender name="SipRollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--历史日志文件输出的文件名 -->
			<FileNamePattern>${LOG_HOME}/sip-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--日志文件保留天数 -->
			<MaxHistory>30</MaxHistory>
			<maxFileSize>50MB</maxFileSize>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}:%L - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>


	<!-- 日志输出级别 -->
	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="websocket" />
		<appender-ref ref="RollingFile"/>
	</root>

	<logger name="com.genersoft.iot.vmp.gb28181.conf.StackLoggerImpl" level="info" additivity="true">
		<appender-ref ref="SipRollingFile" />
	</logger>

</configuration>