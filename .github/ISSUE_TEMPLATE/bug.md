---
name: "[ BUG ] "
about: 关于wvp的bug，与zlm有关的建议直接在zlm的issue中提问
title: 'BUG'
labels: 'wvp的bug'
assignees: ''

---

**环境信息:**

 - 1. 部署方式 wvp-pro docker / zlm(docker) + 编译wvp-pro/ wvp-prp + zlm都是编译部署/
 - 2. 部署环境 windows / ubuntu/ centos ...
 - 3. 端口开放情况
 - 4. 是否是公网部署 
 - 5. 是否使用https
 - 6. 接入设备/平台品牌
 - 7. 你做过哪些尝试
 - 8. 代码更新时间
 - 9. 是否是4G设备接入

**描述错误**
描述下您遇到的问题

**如何复现**
有明确复现步骤的问题会很容易被解决

**截图**  

**抓包文件**
  
**日志**
```
日志内容放这里， 文件的话请直接上传
```


